import React from 'react'
import { NavigateFunction } from 'react-router-dom'
import { TechnicalSkill } from './types'

// Helper function to render icons safely
export const renderIcon = (IconComponent: any, className: string) => {
  if (typeof IconComponent === 'function') {
    return React.createElement(IconComponent, { className })
  }
  return null
}

// Enhanced navigation function that scrolls to top before navigating
export const createNavigationHandler = (
  navigate: NavigateFunction,
  scrollToTopInstant: () => void
) => {
  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}

// Get skills by category - updated for new category mappings
export const getSkillsByCategory = (skills: TechnicalSkill[], category: string) => {
  if (category === "All") return skills

  switch (category) {
    case "Frontend":
      return skills.filter(skill =>
        skill.category === "Frontend" ||
        skill.category === "Styling" ||
        skill.category === "Animation"
      )
    case "Backend":
      return skills.filter(skill => skill.category === "Backend")
    case "Database & DevOps":
      return skills.filter(skill =>
        skill.category === "Database" ||
        skill.category === "Tools"
      )
    case "Testing":
      return skills.filter(skill => skill.category === "Testing")
    default:
      return skills.filter(skill => skill.category === category)
  }
}

// Get skill level color
export const getSkillLevelColor = (level: number) => {
  if (level >= 90) return "text-green-600 dark:text-green-400"
  if (level >= 80) return "text-blue-600 dark:text-blue-400"
  if (level >= 70) return "text-yellow-600 dark:text-yellow-400"
  return "text-orange-600 dark:text-orange-400"
}

// Get skill level background
export const getSkillLevelBg = (level: number) => {
  if (level >= 90) return "bg-green-500/10 border-green-500/20"
  if (level >= 80) return "bg-blue-500/10 border-blue-500/20"
  if (level >= 70) return "bg-yellow-500/10 border-yellow-500/20"
  return "bg-orange-500/10 border-orange-500/20"
}

// Get priority color
export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "High": return "bg-red-500/10 text-red-600 border-red-500/20"
    case "Medium": return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
    case "Low": return "bg-green-500/10 text-green-600 border-green-500/20"
    default: return "bg-gray-500/10 text-gray-600 border-gray-500/20"
  }
}

// Roadmap-specific utility functions

/**
 * Get color based on skill status
 */
export const getStatusColor = (status: string): string => {
  const colors = {
    completed: 'text-green-500 border-green-500/20 bg-green-500/10',
    'in-progress': 'text-yellow-500 border-yellow-500/20 bg-yellow-500/10',
    available: 'text-blue-500 border-blue-500/20 bg-blue-500/10',
    locked: 'text-gray-500 border-gray-500/20 bg-gray-500/10'
  }

  return colors[status as keyof typeof colors] || colors.locked
}

/**
 * Get difficulty color
 */
export const getDifficultyColor = (difficulty: string): string => {
  const colors = {
    beginner: 'text-green-400 bg-green-400/10 border-green-400/20',
    intermediate: 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20',
    advanced: 'text-orange-400 bg-orange-400/10 border-orange-400/20',
    expert: 'text-red-400 bg-red-400/10 border-red-400/20'
  }

  return colors[difficulty as keyof typeof colors] || colors.beginner
}

/**
 * Format duration in hours to human readable format
 */
export const formatDuration = (hours: number): string => {
  if (hours < 1) {
    return `${Math.round(hours * 60)}min`
  } else if (hours < 24) {
    return `${hours}h`
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`
  }
}

/**
 * Calculate completion percentage
 */
export const calculateCompletionPercentage = (completed: number, total: number): number => {
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
}

/**
 * Get learning path color
 */
export const getPathColor = (path: string): string => {
  const colors = {
    frontend: '#06B6D4', // cyan
    backend: '#10B981',  // emerald
    fullstack: '#8B5CF6', // violet
    devops: '#F59E0B',   // amber
    design: '#EC4899'    // pink
  }

  return colors[path as keyof typeof colors] || '#6B7280' // gray as fallback
}
