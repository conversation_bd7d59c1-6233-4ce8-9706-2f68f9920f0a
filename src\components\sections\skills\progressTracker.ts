import { <PERSON>mapNode, Skill<PERSON>tatus, <PERSON><PERSON>ath, SkillsRoadmap } from './types'

export interface ProgressStats {
  overall: {
    completed: number
    inProgress: number
    available: number
    locked: number
    totalSkills: number
    completionPercentage: number
    totalHours: number
    completedHours: number
  }
  byPath: Record<LearningPath, {
    completed: number
    inProgress: number
    available: number
    locked: number
    total: number
    completionPercentage: number
    estimatedHours: number
    completedHours: number
  }>
  byDifficulty: Record<string, {
    completed: number
    total: number
    completionPercentage: number
  }>
  recommendations: {
    nextSkills: RoadmapNode[]
    suggestedPaths: LearningPath[]
    estimatedTimeToNext: number
  }
}

export interface LearningRecommendation {
  skill: RoadmapNode
  reason: string
  priority: 'high' | 'medium' | 'low'
  estimatedTime: number
  prerequisites: RoadmapNode[]
}

export class SkillsProgressTracker {
  private roadmap: SkillsRoadmap

  constructor(roadmap: SkillsRoadmap) {
    this.roadmap = roadmap
  }

  /**
   * Calculate comprehensive progress statistics
   */
  calculateProgress(): ProgressStats {
    const nodes = this.roadmap.nodes
    
    // Overall statistics
    const overall = this.calculateOverallProgress(nodes)
    
    // Progress by learning path
    const byPath = this.calculateProgressByPath(nodes)
    
    // Progress by difficulty
    const byDifficulty = this.calculateProgressByDifficulty(nodes)
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(nodes)

    return {
      overall,
      byPath,
      byDifficulty,
      recommendations
    }
  }

  private calculateOverallProgress(nodes: RoadmapNode[]) {
    const statusCounts = this.getStatusCounts(nodes)
    const totalSkills = nodes.length
    const completionPercentage = Math.round((statusCounts.completed / totalSkills) * 100)
    
    const totalHours = nodes.reduce((sum, node) => sum + node.estimatedHours, 0)
    const completedHours = nodes
      .filter(node => node.status === 'completed')
      .reduce((sum, node) => sum + node.estimatedHours, 0)

    return {
      ...statusCounts,
      totalSkills,
      completionPercentage,
      totalHours,
      completedHours
    }
  }

  private calculateProgressByPath(nodes: RoadmapNode[]) {
    const paths: LearningPath[] = ['frontend', 'backend', 'fullstack', 'devops', 'design']
    const result: Record<LearningPath, any> = {} as any

    paths.forEach(path => {
      const pathNodes = nodes.filter(node => node.path.includes(path))
      const statusCounts = this.getStatusCounts(pathNodes)
      const total = pathNodes.length
      const completionPercentage = total > 0 ? Math.round((statusCounts.completed / total) * 100) : 0
      
      const estimatedHours = pathNodes.reduce((sum, node) => sum + node.estimatedHours, 0)
      const completedHours = pathNodes
        .filter(node => node.status === 'completed')
        .reduce((sum, node) => sum + node.estimatedHours, 0)

      result[path] = {
        ...statusCounts,
        total,
        completionPercentage,
        estimatedHours,
        completedHours
      }
    })

    return result
  }

  private calculateProgressByDifficulty(nodes: RoadmapNode[]) {
    const difficulties = ['beginner', 'intermediate', 'advanced', 'expert']
    const result: Record<string, any> = {}

    difficulties.forEach(difficulty => {
      const difficultyNodes = nodes.filter(node => node.difficulty === difficulty)
      const completed = difficultyNodes.filter(node => node.status === 'completed').length
      const total = difficultyNodes.length
      const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0

      result[difficulty] = {
        completed,
        total,
        completionPercentage
      }
    })

    return result
  }

  private generateRecommendations(nodes: RoadmapNode[]) {
    const nextSkills = this.getNextAvailableSkills(nodes)
    const suggestedPaths = this.getSuggestedPaths(nodes)
    const estimatedTimeToNext = this.calculateTimeToNextMilestone(nodes)

    return {
      nextSkills: nextSkills.slice(0, 5), // Top 5 recommendations
      suggestedPaths,
      estimatedTimeToNext
    }
  }

  private getStatusCounts(nodes: RoadmapNode[]) {
    return nodes.reduce((counts, node) => {
      const status = this.getActualNodeStatus(node)
      counts[status]++
      return counts
    }, {
      completed: 0,
      'in-progress': 0,
      available: 0,
      locked: 0
    })
  }

  /**
   * Determine actual node status based on prerequisites
   */
  private getActualNodeStatus(node: RoadmapNode): SkillStatus {
    if (node.status === 'completed' || node.status === 'in-progress') {
      return node.status
    }
    
    // Check if prerequisites are met
    const prerequisitesMet = node.prerequisites.every(prereqId => {
      const prereqNode = this.roadmap.nodes.find(n => n.id === prereqId)
      return prereqNode?.status === 'completed'
    })
    
    return prerequisitesMet ? 'available' : 'locked'
  }

  /**
   * Get next available skills that can be started
   */
  private getNextAvailableSkills(nodes: RoadmapNode[]): RoadmapNode[] {
    return nodes
      .filter(node => this.getActualNodeStatus(node) === 'available')
      .sort((a, b) => {
        // Prioritize by difficulty (easier first) and estimated hours (shorter first)
        const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 }
        const aDifficulty = difficultyOrder[a.difficulty]
        const bDifficulty = difficultyOrder[b.difficulty]
        
        if (aDifficulty !== bDifficulty) {
          return aDifficulty - bDifficulty
        }
        
        return a.estimatedHours - b.estimatedHours
      })
  }

  /**
   * Suggest learning paths based on current progress
   */
  private getSuggestedPaths(nodes: RoadmapNode[]): LearningPath[] {
    const pathProgress = this.calculateProgressByPath(nodes)
    
    // Sort paths by completion percentage (ascending) to suggest paths with room for growth
    return Object.entries(pathProgress)
      .filter(([_, progress]) => progress.total > 0) // Only include paths with skills
      .sort(([_, a], [__, b]) => a.completionPercentage - b.completionPercentage)
      .slice(0, 3) // Top 3 suggested paths
      .map(([path]) => path as LearningPath)
  }

  /**
   * Calculate estimated time to next major milestone
   */
  private calculateTimeToNextMilestone(nodes: RoadmapNode[]): number {
    const inProgressNodes = nodes.filter(node => node.status === 'in-progress')
    const availableNodes = this.getNextAvailableSkills(nodes).slice(0, 3)
    
    const inProgressHours = inProgressNodes.reduce((sum, node) => {
      // Assume 50% completion for in-progress skills
      return sum + (node.estimatedHours * 0.5)
    }, 0)
    
    const nextAvailableHours = availableNodes.reduce((sum, node) => sum + node.estimatedHours, 0)
    
    return Math.round(inProgressHours + nextAvailableHours)
  }

  /**
   * Get detailed learning recommendations with reasoning
   */
  getLearningRecommendations(): LearningRecommendation[] {
    const nodes = this.roadmap.nodes
    const availableSkills = this.getNextAvailableSkills(nodes)
    
    return availableSkills.slice(0, 5).map(skill => {
      const prerequisites = skill.prerequisites.map(id => 
        nodes.find(n => n.id === id)!
      ).filter(Boolean)
      
      let reason = 'Ready to start - all prerequisites completed'
      let priority: 'high' | 'medium' | 'low' = 'medium'
      
      // Determine priority and reason
      if (skill.difficulty === 'beginner') {
        priority = 'high'
        reason = 'Foundation skill - builds strong base for advanced topics'
      } else if (skill.path.includes('fullstack')) {
        priority = 'high'
        reason = 'Full-stack skill - valuable for complete development capability'
      } else if (skill.estimatedHours <= 40) {
        priority = 'high'
        reason = 'Quick win - can be completed in reasonable time'
      } else if (skill.difficulty === 'expert') {
        priority = 'low'
        reason = 'Advanced skill - consider after mastering intermediate skills'
      }
      
      return {
        skill,
        reason,
        priority,
        estimatedTime: skill.estimatedHours,
        prerequisites
      }
    })
  }

  /**
   * Get skills that will be unlocked by completing a specific skill
   */
  getUnlockedSkills(skillId: string): RoadmapNode[] {
    const skill = this.roadmap.nodes.find(n => n.id === skillId)
    if (!skill) return []
    
    return skill.unlocks.map(id => 
      this.roadmap.nodes.find(n => n.id === id)!
    ).filter(Boolean)
  }

  /**
   * Calculate the impact of completing a skill (how many skills it unlocks)
   */
  calculateSkillImpact(skillId: string): number {
    const directUnlocks = this.getUnlockedSkills(skillId)
    
    // Calculate cascading unlocks (skills that become available after direct unlocks)
    let totalImpact = directUnlocks.length
    
    directUnlocks.forEach(unlockedSkill => {
      const cascadingUnlocks = this.getUnlockedSkills(unlockedSkill.id)
      totalImpact += cascadingUnlocks.length
    })
    
    return totalImpact
  }
}
