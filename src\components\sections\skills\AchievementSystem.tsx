import React, { useMemo } from 'react'
import { motion } from 'framer-motion'
import { Trophy, Star, Target, Zap, Award, Crown, Shield, Flame, Gem, Medal } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { skillsRoadmap } from './roadmapData'
import { SkillsProgressTracker } from './progressTracker'
import { itemVariants } from './types'

interface Achievement {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  category: 'completion' | 'streak' | 'difficulty' | 'path' | 'time' | 'special'
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  requirement: number
  currentProgress: number
  isUnlocked: boolean
  unlockedAt?: Date
  points: number
}

interface AchievementSystemProps {
  className?: string
}

const achievementIcons = {
  completion: Trophy,
  streak: Flame,
  difficulty: Crown,
  path: Target,
  time: Zap,
  special: Gem
}

const tierColors = {
  bronze: 'text-amber-600 bg-amber-600/10 border-amber-600/20',
  silver: 'text-gray-400 bg-gray-400/10 border-gray-400/20',
  gold: 'text-yellow-500 bg-yellow-500/10 border-yellow-500/20',
  platinum: 'text-purple-500 bg-purple-500/10 border-purple-500/20'
}

const tierPoints = {
  bronze: 10,
  silver: 25,
  gold: 50,
  platinum: 100
}

export default function AchievementSystem({ className }: AchievementSystemProps) {
  const progressTracker = useMemo(() => new SkillsProgressTracker(skillsRoadmap), [])
  const progressStats = useMemo(() => progressTracker.calculateProgress(), [progressTracker])

  const achievements = useMemo((): Achievement[] => {
    const completedSkills = progressStats.overall.completed
    const totalSkills = progressStats.overall.totalSkills
    const completionPercentage = progressStats.overall.completionPercentage
    
    return [
      // Completion Achievements
      {
        id: 'first-skill',
        name: 'First Steps',
        description: 'Complete your first skill',
        icon: Star,
        category: 'completion',
        tier: 'bronze',
        requirement: 1,
        currentProgress: completedSkills,
        isUnlocked: completedSkills >= 1,
        points: tierPoints.bronze
      },
      {
        id: 'skill-collector',
        name: 'Skill Collector',
        description: 'Complete 5 skills',
        icon: Trophy,
        category: 'completion',
        tier: 'silver',
        requirement: 5,
        currentProgress: completedSkills,
        isUnlocked: completedSkills >= 5,
        points: tierPoints.silver
      },
      {
        id: 'skill-master',
        name: 'Skill Master',
        description: 'Complete 10 skills',
        icon: Award,
        category: 'completion',
        tier: 'gold',
        requirement: 10,
        currentProgress: completedSkills,
        isUnlocked: completedSkills >= 10,
        points: tierPoints.gold
      },
      {
        id: 'skill-legend',
        name: 'Skill Legend',
        description: 'Complete all skills',
        icon: Crown,
        category: 'completion',
        tier: 'platinum',
        requirement: totalSkills,
        currentProgress: completedSkills,
        isUnlocked: completedSkills >= totalSkills,
        points: tierPoints.platinum
      },
      
      // Path Achievements
      {
        id: 'frontend-specialist',
        name: 'Frontend Specialist',
        description: 'Complete all frontend skills',
        icon: Target,
        category: 'path',
        tier: 'gold',
        requirement: progressStats.byPath.frontend.total,
        currentProgress: progressStats.byPath.frontend.completed,
        isUnlocked: progressStats.byPath.frontend.completed >= progressStats.byPath.frontend.total,
        points: tierPoints.gold
      },
      {
        id: 'backend-expert',
        name: 'Backend Expert',
        description: 'Complete all backend skills',
        icon: Shield,
        category: 'path',
        tier: 'gold',
        requirement: progressStats.byPath.backend.total,
        currentProgress: progressStats.byPath.backend.completed,
        isUnlocked: progressStats.byPath.backend.completed >= progressStats.byPath.backend.total,
        points: tierPoints.gold
      },
      {
        id: 'fullstack-hero',
        name: 'Full-Stack Hero',
        description: 'Complete all full-stack skills',
        icon: Gem,
        category: 'path',
        tier: 'platinum',
        requirement: progressStats.byPath.fullstack.total,
        currentProgress: progressStats.byPath.fullstack.completed,
        isUnlocked: progressStats.byPath.fullstack.completed >= progressStats.byPath.fullstack.total,
        points: tierPoints.platinum
      },
      
      // Difficulty Achievements
      {
        id: 'beginner-graduate',
        name: 'Beginner Graduate',
        description: 'Complete all beginner skills',
        icon: Medal,
        category: 'difficulty',
        tier: 'bronze',
        requirement: progressStats.byDifficulty.beginner.total,
        currentProgress: progressStats.byDifficulty.beginner.completed,
        isUnlocked: progressStats.byDifficulty.beginner.completed >= progressStats.byDifficulty.beginner.total,
        points: tierPoints.bronze
      },
      {
        id: 'intermediate-achiever',
        name: 'Intermediate Achiever',
        description: 'Complete all intermediate skills',
        icon: Trophy,
        category: 'difficulty',
        tier: 'silver',
        requirement: progressStats.byDifficulty.intermediate.total,
        currentProgress: progressStats.byDifficulty.intermediate.completed,
        isUnlocked: progressStats.byDifficulty.intermediate.completed >= progressStats.byDifficulty.intermediate.total,
        points: tierPoints.silver
      },
      {
        id: 'advanced-master',
        name: 'Advanced Master',
        description: 'Complete all advanced skills',
        icon: Crown,
        category: 'difficulty',
        tier: 'gold',
        requirement: progressStats.byDifficulty.advanced.total,
        currentProgress: progressStats.byDifficulty.advanced.completed,
        isUnlocked: progressStats.byDifficulty.advanced.completed >= progressStats.byDifficulty.advanced.total,
        points: tierPoints.gold
      },
      
      // Milestone Achievements
      {
        id: 'quarter-complete',
        name: 'Quarter Complete',
        description: 'Complete 25% of all skills',
        icon: Target,
        category: 'completion',
        tier: 'bronze',
        requirement: 25,
        currentProgress: completionPercentage,
        isUnlocked: completionPercentage >= 25,
        points: tierPoints.bronze
      },
      {
        id: 'half-complete',
        name: 'Halfway There',
        description: 'Complete 50% of all skills',
        icon: Flame,
        category: 'completion',
        tier: 'silver',
        requirement: 50,
        currentProgress: completionPercentage,
        isUnlocked: completionPercentage >= 50,
        points: tierPoints.silver
      },
      {
        id: 'three-quarters-complete',
        name: 'Almost There',
        description: 'Complete 75% of all skills',
        icon: Star,
        category: 'completion',
        tier: 'gold',
        requirement: 75,
        currentProgress: completionPercentage,
        isUnlocked: completionPercentage >= 75,
        points: tierPoints.gold
      }
    ]
  }, [progressStats])

  const unlockedAchievements = achievements.filter(a => a.isUnlocked)
  const totalPoints = unlockedAchievements.reduce((sum, a) => sum + a.points, 0)
  const nextAchievements = achievements
    .filter(a => !a.isUnlocked)
    .sort((a, b) => {
      const aProgress = a.currentProgress / a.requirement
      const bProgress = b.currentProgress / b.requirement
      return bProgress - aProgress
    })
    .slice(0, 3)

  return (
    <div className={cn('space-y-6', className)}>
      {/* Achievement Overview */}
      <motion.div variants={itemVariants}>
        <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="w-5 h-5" />
              Achievement System
            </CardTitle>
            <CardDescription>
              Track your learning milestones and unlock rewards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-primary">{unlockedAchievements.length}</div>
                <div className="text-sm text-muted-foreground">Unlocked</div>
              </div>
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-yellow-500">{totalPoints}</div>
                <div className="text-sm text-muted-foreground">Points</div>
              </div>
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-blue-500">{achievements.length - unlockedAchievements.length}</div>
                <div className="text-sm text-muted-foreground">Remaining</div>
              </div>
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-purple-500">
                  {Math.round((unlockedAchievements.length / achievements.length) * 100)}%
                </div>
                <div className="text-sm text-muted-foreground">Complete</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Achievements */}
      {unlockedAchievements.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Unlocked Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {unlockedAchievements.map((achievement) => {
                  const IconComponent = achievement.icon
                  return (
                    <div
                      key={achievement.id}
                      className={cn(
                        "p-4 rounded-lg border transition-all duration-300",
                        tierColors[achievement.tier]
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center">
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold text-sm">{achievement.name}</h4>
                            <Badge variant="outline" className="text-xs capitalize">
                              {achievement.tier}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {achievement.description}
                          </p>
                          <div className="text-xs text-primary font-medium">
                            +{achievement.points} points
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Next Achievements */}
      {nextAchievements.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Next Achievements
              </CardTitle>
              <CardDescription>
                Achievements you're closest to unlocking
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {nextAchievements.map((achievement) => {
                const IconComponent = achievement.icon
                const progressPercentage = Math.min(100, (achievement.currentProgress / achievement.requirement) * 100)
                
                return (
                  <div key={achievement.id} className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center opacity-60">
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-sm">{achievement.name}</h4>
                            <p className="text-xs text-muted-foreground">
                              {achievement.description}
                            </p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline" className="text-xs capitalize">
                              {achievement.tier}
                            </Badge>
                            <div className="text-xs text-muted-foreground mt-1">
                              +{achievement.points} points
                            </div>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span>Progress</span>
                            <span>{achievement.currentProgress}/{achievement.requirement}</span>
                          </div>
                          <Progress value={progressPercentage} className="h-2" />
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
