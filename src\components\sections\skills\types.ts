import { LucideIcon } from 'lucide-react'

// Base interface for all skills sections
export interface BaseSectionProps {
  className?: string
}

// Technical skill interface
export interface TechnicalSkill {
  name: string
  level: number
  category: string
  icon: any
  experience: string
  description: string
  projects: number
  color: string
}

// Soft skill interface
export interface SoftSkill {
  name: string
  icon: LucideIcon
  description: string
  examples: string[]
  strength: number
}

// Learning goal interface
export interface LearningGoal {
  name: string
  icon: LucideIcon
  description: string
  timeline: string
  priority: string
  progress: number
  resources: string[]
}

// Category filter interface
export interface CategoryFilter {
  name: string
  icon: LucideIcon
  count: number
}

// Roadmap-specific types
export type SkillStatus = 'completed' | 'in-progress' | 'available' | 'locked'
export type SkillDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert'
export type LearningPath = 'frontend' | 'backend' | 'fullstack' | 'devops' | 'design'

// Roadmap node interface
export interface RoadmapNode {
  id: string
  name: string
  description: string
  icon: any
  status: SkillStatus
  difficulty: SkillDifficulty
  estimatedHours: number
  category: string
  path: LearningPath[]
  prerequisites: string[] // Array of node IDs
  unlocks: string[] // Array of node IDs this skill unlocks
  position: {
    x: number
    y: number
  }
  resources: {
    name: string
    type: 'documentation' | 'course' | 'tutorial' | 'book' | 'practice'
    url?: string
    free: boolean
  }[]
  projects?: string[] // Related project examples
  color: string
  level?: number // Current proficiency level (0-100)
}

// Roadmap connection interface
export interface RoadmapConnection {
  from: string
  to: string
  type: 'prerequisite' | 'recommended' | 'optional'
  strength: number // Visual weight of the connection (1-3)
}

// Roadmap section interface for organizing nodes
export interface RoadmapSection {
  id: string
  name: string
  description: string
  color: string
  nodes: string[] // Array of node IDs in this section
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

// Complete roadmap interface
export interface SkillsRoadmap {
  nodes: RoadmapNode[]
  connections: RoadmapConnection[]
  sections: RoadmapSection[]
  metadata: {
    totalSkills: number
    completedSkills: number
    inProgressSkills: number
    estimatedTotalHours: number
    lastUpdated: string
  }
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
