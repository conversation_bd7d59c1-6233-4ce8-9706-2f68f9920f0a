import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { skillsRoadmap } from './roadmapData'
import { RoadmapNode } from './types'
import { renderIcon } from './utils'
import SkillNodeDetail from './SkillNodeDetail'

interface SkillsRoadmapVisualizationProps {
  className?: string
}

// Node styling similar to roadmap.sh
const getNodeStyles = (node: RoadmapNode) => {
  const isCompleted = node.status === 'completed'
  const isFoundation = node.category === 'Foundation'

  if (isCompleted || isFoundation) {
    return {
      backgroundColor: '#FDE047', // Yellow like roadmap.sh
      color: '#000000',
      border: '2px solid #EAB308'
    }
  }

  return {
    backgroundColor: '#374151', // Dark like roadmap.sh checkpoints
    color: '#FFFFFF',
    border: '2px solid #4B5563'
  }
}

// Connection line component
const ConnectionLine = ({ from, to, isVertical = true }: { from: { x: number, y: number }, to: { x: number, y: number }, isVertical?: boolean }) => {
  if (isVertical) {
    return (
      <div
        className="absolute border-l-2 border-blue-400 border-dashed"
        style={{
          left: `${from.x + 60}px`, // Center of node (120px width / 2)
          top: `${from.y + 60}px`,
          height: `${to.y - from.y - 60}px`,
          zIndex: 1
        }}
      />
    )
  }

  return (
    <div
      className="absolute border-t-2 border-blue-400 border-dashed"
      style={{
        left: `${Math.min(from.x, to.x) + 60}px`,
        top: `${from.y + 30}px`,
        width: `${Math.abs(to.x - from.x)}px`,
        zIndex: 1
      }}
    />
  )
}

// Roadmap Node Component
const RoadmapNodeComponent = ({ node, onClick }: { node: RoadmapNode, onClick: () => void }) => {
  const styles = getNodeStyles(node)

  return (
    <motion.div
      className="absolute cursor-pointer rounded-lg px-4 py-2 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200 z-10"
      style={{
        left: `${node.position.x}px`,
        top: `${node.position.y}px`,
        backgroundColor: styles.backgroundColor,
        color: styles.color,
        border: styles.border,
        minWidth: '120px',
        textAlign: 'center'
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
    >
      <div className="flex items-center justify-center gap-2">
        <div className="w-4 h-4">
          {renderIcon(node.icon, "w-4 h-4")}
        </div>
        <span className="font-semibold">{node.name}</span>
      </div>
      {node.estimatedHours && (
        <div className="text-xs opacity-75 mt-1">
          {node.estimatedHours}h
        </div>
      )}
    </motion.div>
  )
}

export default function SkillsRoadmapVisualization({ className }: SkillsRoadmapVisualizationProps) {
  const [selectedNode, setSelectedNode] = useState<RoadmapNode | null>(null)

  // Get all nodes from the roadmap data
  const allNodes = skillsRoadmap.nodes || []

  // Create connections between nodes based on prerequisites
  const connections = allNodes.flatMap(node =>
    node.prerequisites?.map(prereqId => {
      const prereqNode = allNodes.find(n => n.id === prereqId)
      if (prereqNode) {
        return { from: prereqNode.position, to: node.position }
      }
      return null
    }).filter(Boolean) || []
  )

  return (
    <div className={cn('w-full', className)}>
      {/* Roadmap Container */}
      <div className="relative w-full min-h-[1000px] bg-white rounded-lg border border-gray-200 overflow-hidden">
        {/* Connection Lines */}
        {connections.map((connection, index) => (
          <ConnectionLine
            key={index}
            from={connection.from}
            to={connection.to}
            isVertical={Math.abs(connection.to.y - connection.from.y) > Math.abs(connection.to.x - connection.from.x)}
          />
        ))}

        {/* Roadmap Nodes */}
        {allNodes.map((node) => (
          <RoadmapNodeComponent
            key={node.id}
            node={node}
            onClick={() => setSelectedNode(node)}
          />
        ))}

        {/* Progress Path Line (main vertical line) */}
        <div
          className="absolute left-1/2 top-0 bottom-0 w-1 bg-blue-400 transform -translate-x-1/2 z-0"
          style={{ opacity: 0.3 }}
        />
      </div>

      {/* Node Detail Modal */}
      <AnimatePresence>
        {selectedNode && (
          <SkillNodeDetail
            node={selectedNode}
            onClose={() => setSelectedNode(null)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}