import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ZoomIn, ZoomOut, RotateCcw, Filter, Info, Clock, Award, Target } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { skillsRoadmap } from './roadmapData'
import { RoadmapNode, SkillStatus, LearningPath } from './types'
import { renderIcon } from './utils'
import SkillNodeDetail from './SkillNodeDetail'

interface SkillsRoadmapVisualizationProps {
  className?: string
}

interface ViewBox {
  x: number
  y: number
  width: number
  height: number
  scale: number
}

const statusColors = {
  completed: '#10B981',
  'in-progress': '#F59E0B',
  available: '#6B7280',
  locked: '#374151'
}

const difficultyColors = {
  beginner: '#10B981',
  intermediate: '#F59E0B',
  advanced: '#EF4444',
  expert: '#8B5CF6'
}

export default function SkillsRoadmapVisualization({ className }: SkillsRoadmapVisualizationProps) {
  const [selectedNode, setSelectedNode] = useState<RoadmapNode | null>(null)
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState<LearningPath | 'all'>('all')
  const [viewBox, setViewBox] = useState<ViewBox>({ x: 0, y: 0, width: 900, height: 1200, scale: 1 })
  const svgRef = useRef<SVGSVGElement>(null)

  const filteredNodes = useMemo(() =>
    skillsRoadmap.nodes.filter(node =>
      activeFilter === 'all' || node.path.includes(activeFilter)
    ), [activeFilter]
  )

  const filteredConnections = useMemo(() =>
    skillsRoadmap.connections.filter(connection =>
      filteredNodes.some(node => node.id === connection.from) &&
      filteredNodes.some(node => node.id === connection.to)
    ), [filteredNodes]
  )

  const handleZoom = (factor: number) => {
    setViewBox(prev => ({
      ...prev,
      scale: Math.max(0.5, Math.min(2, prev.scale * factor))
    }))
  }

  const handleReset = () => {
    setViewBox({ x: 0, y: 0, width: 900, height: 1200, scale: 1 })
    setSelectedNode(null)
    setActiveFilter('all')
  }

  // Touch and pan state
  const [isPanning, setIsPanning] = useState(false)
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 })

  // Touch-friendly pan handlers
  const handlePointerDown = (e: React.PointerEvent) => {
    setIsPanning(true)
    setLastPanPoint({ x: e.clientX, y: e.clientY })
    e.currentTarget.setPointerCapture(e.pointerId)
  }

  const handlePointerMove = (e: React.PointerEvent) => {
    if (!isPanning) return

    const deltaX = e.clientX - lastPanPoint.x
    const deltaY = e.clientY - lastPanPoint.y

    setViewBox(prev => ({
      ...prev,
      x: prev.x - deltaX / prev.scale,
      y: prev.y - deltaY / prev.scale
    }))

    setLastPanPoint({ x: e.clientX, y: e.clientY })
  }

  const handlePointerUp = (e: React.PointerEvent) => {
    setIsPanning(false)
    e.currentTarget.releasePointerCapture(e.pointerId)
  }

  // Wheel zoom for desktop - using useEffect to add passive: false
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      e.preventDefault()
      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
      handleZoom(zoomFactor)
    }

    const svgElement = svgRef.current
    if (svgElement) {
      svgElement.addEventListener('wheel', handleWheel, { passive: false })
      return () => {
        svgElement.removeEventListener('wheel', handleWheel)
      }
    }
  }, [])

  const getNodeStatus = (node: RoadmapNode): SkillStatus => {
    // Logic to determine if a node should be available based on prerequisites
    if (node.status === 'completed' || node.status === 'in-progress') {
      return node.status
    }
    
    const prerequisitesMet = node.prerequisites.every(prereqId =>
      skillsRoadmap.nodes.find(n => n.id === prereqId)?.status === 'completed'
    )
    
    return prerequisitesMet ? 'available' : 'locked'
  }

  const renderConnection = (connection: any, index: number) => {
    const fromNode = filteredNodes.find(n => n.id === connection.from)
    const toNode = filteredNodes.find(n => n.id === connection.to)
    
    if (!fromNode || !toNode) return null

    const strokeWidth = connection.strength
    const strokeColor = connection.type === 'prerequisite' ? '#6B7280' : 
                       connection.type === 'recommended' ? '#10B981' : '#94A3B8'
    const strokeDasharray = connection.type === 'optional' ? '5,5' : 'none'

    return (
      <line
        key={`connection-${index}`}
        x1={fromNode.position.x + 60}
        y1={fromNode.position.y + 30}
        x2={toNode.position.x + 60}
        y2={toNode.position.y + 30}
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeDasharray={strokeDasharray}
        opacity={0.6}
        className="transition-opacity duration-300"
      />
    )
  }

  const renderNode = (node: RoadmapNode) => {
    const status = getNodeStatus(node)
    const isHovered = hoveredNode === node.id
    const isSelected = selectedNode?.id === node.id
    
    return (
      <g key={node.id} transform={`translate(${node.position.x}, ${node.position.y})`}>
        {/* Node shadow */}
        <rect
          x={2}
          y={2}
          width={120}
          height={60}
          rx={12}
          fill="rgba(0,0,0,0.2)"
          className="blur-sm"
        />

        {/* Node background with enhanced styling */}
        <motion.rect
          width={120}
          height={60}
          rx={12}
          fill={statusColors[status]}
          stroke={isSelected ? '#8B5CF6' : isHovered ? difficultyColors[node.difficulty] : 'rgba(255,255,255,0.1)'}
          strokeWidth={isSelected ? 3 : isHovered ? 2 : 1}
          className="cursor-pointer transition-all duration-300"
          style={{
            filter: isHovered ? 'drop-shadow(0 0 10px rgba(139, 92, 246, 0.5))' : 'none'
          }}
          whileHover={{ scale: 1.05 }}
          onMouseEnter={() => setHoveredNode(node.id)}
          onMouseLeave={() => setHoveredNode(null)}
          onClick={() => setSelectedNode(node)}
        />
        
        {/* Node icon */}
        <foreignObject x={10} y={10} width={24} height={24}>
          <div className="w-6 h-6 text-white flex items-center justify-center">
            {renderIcon(node.icon, "w-5 h-5")}
          </div>
        </foreignObject>
        
        {/* Node title */}
        <text
          x={40}
          y={25}
          fill="white"
          fontSize="12"
          fontWeight="600"
          className="pointer-events-none"
        >
          {node.name.length > 12 ? `${node.name.substring(0, 12)}...` : node.name}
        </text>
        
        {/* Difficulty indicator */}
        <circle
          cx={105}
          cy={15}
          r={6}
          fill={difficultyColors[node.difficulty]}
          className="pointer-events-none"
        />
        
        {/* Progress indicator for completed/in-progress */}
        {(status === 'completed' || status === 'in-progress') && node.level && (
          <rect
            x={10}
            y={45}
            width={100 * (node.level / 100)}
            height={4}
            rx={2}
            fill="rgba(255, 255, 255, 0.8)"
            className="pointer-events-none"
          />
        )}
        
        {/* Hover tooltip */}
        <AnimatePresence>
          {isHovered && (
            <motion.g
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              <rect
                x={-20}
                y={-80}
                width={160}
                height={60}
                rx={8}
                fill="rgba(0, 0, 0, 0.9)"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeWidth={1}
                className="pointer-events-none"
              />
              <text
                x={60}
                y={-60}
                fill="white"
                fontSize="10"
                textAnchor="middle"
                className="pointer-events-none"
              >
                {node.description.substring(0, 40)}...
              </text>
              <text
                x={60}
                y={-45}
                fill="#94A3B8"
                fontSize="9"
                textAnchor="middle"
                className="pointer-events-none"
              >
                {node.estimatedHours}h • {node.difficulty}
              </text>
            </motion.g>
          )}
        </AnimatePresence>
      </g>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Progress Overview */}
      <div className="mb-6 p-4 bg-neutral-900/50 rounded-xl border border-neutral-800">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">{skillsRoadmap.metadata.completedSkills}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-500">{skillsRoadmap.metadata.inProgressSkills}</div>
            <div className="text-sm text-muted-foreground">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">{skillsRoadmap.metadata.totalSkills}</div>
            <div className="text-sm text-muted-foreground">Total Skills</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-500">{skillsRoadmap.metadata.estimatedTotalHours}h</div>
            <div className="text-sm text-muted-foreground">Total Hours</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleZoom(1.2)}
            className="flex items-center gap-2 touch-manipulation"
          >
            <ZoomIn className="w-4 h-4" />
            <span className="hidden sm:inline">Zoom In</span>
            <span className="sm:hidden">+</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleZoom(0.8)}
            className="flex items-center gap-2 touch-manipulation"
          >
            <ZoomOut className="w-4 h-4" />
            <span className="hidden sm:inline">Zoom Out</span>
            <span className="sm:hidden">-</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="flex items-center gap-2 touch-manipulation"
          >
            <RotateCcw className="w-4 h-4" />
            <span className="hidden sm:inline">Reset</span>
          </Button>
        </div>

        {/* Path filters */}
        <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
          <Filter className="w-4 h-4 text-muted-foreground hidden sm:block" />
          <div className="flex flex-wrap gap-1 sm:gap-2">
            {(['all', 'frontend', 'backend', 'fullstack'] as const).map((path) => (
              <Button
                key={path}
                variant={activeFilter === path ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(path)}
                className="capitalize text-xs sm:text-sm touch-manipulation"
              >
                {path === 'all' ? 'All' : path}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Roadmap SVG */}
      <div className="relative w-full h-[600px] sm:h-[800px] lg:h-[1000px] bg-gradient-to-br from-neutral-950 via-neutral-900 to-neutral-950 rounded-xl border border-neutral-800/50 overflow-hidden shadow-2xl">
        <svg
          ref={svgRef}
          width="100%"
          height="100%"
          viewBox={`${viewBox.x} ${viewBox.y} ${viewBox.width / viewBox.scale} ${viewBox.height / viewBox.scale}`}
          className={cn(
            "w-full h-full touch-none select-none",
            isPanning ? "cursor-grabbing" : "cursor-grab"
          )}
          onPointerDown={handlePointerDown}
          onPointerMove={handlePointerMove}
          onPointerUp={handlePointerUp}

          style={{ touchAction: 'none' }}
        >
          {/* Background grid */}
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.05)" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Connections */}
          <g className="connections">
            {filteredConnections.map(renderConnection)}
          </g>

          {/* Nodes */}
          <g className="nodes">
            {filteredNodes.map(renderNode)}
          </g>
        </svg>
      </div>

      {/* Skill Detail Modal */}
      <SkillNodeDetail
        node={selectedNode}
        isOpen={!!selectedNode}
        onClose={() => setSelectedNode(null)}
      />
    </div>
  )
}
