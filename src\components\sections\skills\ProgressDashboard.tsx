import React, { useMemo } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp, Target, Clock, Award, Lightbulb, ArrowRight, CheckCircle, Circle, Lock, AlertCircle, Trophy } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import { skillsRoadmap } from './roadmapData'
import { SkillsProgressTracker } from './progressTracker'
import { renderIcon } from './utils'
import { itemVariants } from './types'
import AchievementSystem from './AchievementSystem'

interface ProgressDashboardProps {
  className?: string
  onSkillSelect?: (skillId: string) => void
}

const statusIcons = {
  completed: CheckCircle,
  'in-progress': Circle,
  available: Target,
  locked: Lock
}

const statusColors = {
  completed: 'text-green-500',
  'in-progress': 'text-yellow-500',
  available: 'text-blue-500',
  locked: 'text-gray-500'
}

const priorityColors = {
  high: 'border-red-500/20 bg-red-500/5',
  medium: 'border-yellow-500/20 bg-yellow-500/5',
  low: 'border-blue-500/20 bg-blue-500/5'
}

export default function ProgressDashboard({ className, onSkillSelect }: ProgressDashboardProps) {
  const progressTracker = useMemo(() => new SkillsProgressTracker(skillsRoadmap), [])
  const progressStats = useMemo(() => progressTracker.calculateProgress(), [progressTracker])
  const recommendations = useMemo(() => progressTracker.getLearningRecommendations(), [progressTracker])

  return (
    <div className={cn('space-y-8', className)}>
      <Tabs defaultValue="progress" className="w-full">
        <TabsList className="grid w-full max-w-md mx-auto grid-cols-2">
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center gap-2">
            <Trophy className="w-4 h-4" />
            Achievements
          </TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="mt-8 space-y-8">
      {/* Overall Progress Overview */}
      <motion.div variants={itemVariants}>
        <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Learning Progress Overview
            </CardTitle>
            <CardDescription>
              Your comprehensive skill development journey
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              <div className="text-center space-y-1 sm:space-y-2">
                <div className="text-2xl sm:text-3xl font-bold text-green-500">
                  {progressStats.overall.completed}
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">Completed</div>
                <div className="text-xs text-muted-foreground hidden sm:block">
                  {progressStats.overall.completedHours}h invested
                </div>
              </div>
              <div className="text-center space-y-1 sm:space-y-2">
                <div className="text-2xl sm:text-3xl font-bold text-yellow-500">
                  {progressStats.overall.inProgress}
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">In Progress</div>
                <div className="text-xs text-muted-foreground hidden sm:block">
                  ~{Math.round(progressStats.recommendations.estimatedTimeToNext)}h remaining
                </div>
              </div>
              <div className="text-center space-y-1 sm:space-y-2">
                <div className="text-2xl sm:text-3xl font-bold text-blue-500">
                  {progressStats.overall.available}
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">Available</div>
                <div className="text-xs text-muted-foreground hidden sm:block">Ready to start</div>
              </div>
              <div className="text-center space-y-1 sm:space-y-2">
                <div className="text-2xl sm:text-3xl font-bold text-primary">
                  {progressStats.overall.completionPercentage}%
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">Overall</div>
                <div className="text-xs text-muted-foreground hidden sm:block">
                  {progressStats.overall.totalSkills} total skills
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {progressStats.overall.completionPercentage}%
                </span>
              </div>
              <Progress value={progressStats.overall.completionPercentage} className="h-3" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Learning Path Progress */}
      <motion.div variants={itemVariants}>
        <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Progress by Learning Path
            </CardTitle>
            <CardDescription>
              Track your advancement across different development areas
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {Object.entries(progressStats.byPath)
              .filter(([_, progress]) => progress.total > 0)
              .map(([path, progress]) => (
                <div key={path} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="capitalize font-medium text-lg">{path} Development</span>
                      <Badge variant="outline" className="text-xs">
                        {progress.completed}/{progress.total} skills
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{progress.completionPercentage}%</div>
                      <div className="text-xs text-muted-foreground">
                        {progress.completedHours}h / {progress.estimatedHours}h
                      </div>
                    </div>
                  </div>
                  <Progress value={progress.completionPercentage} className="h-2" />
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      {progress.completed} completed
                    </span>
                    <span className="flex items-center gap-1">
                      <Circle className="w-3 h-3 text-yellow-500" />
                      {progress.inProgress} in progress
                    </span>
                    <span className="flex items-center gap-1">
                      <Target className="w-3 h-3 text-blue-500" />
                      {progress.available} available
                    </span>
                  </div>
                </div>
              ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Learning Recommendations */}
      <motion.div variants={itemVariants}>
        <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Personalized Learning Recommendations
            </CardTitle>
            <CardDescription>
              Next skills to focus on based on your current progress
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recommendations.map((recommendation, index) => {
              const StatusIcon = statusIcons[recommendation.skill.status]
              return (
                <div
                  key={recommendation.skill.id}
                  className={cn(
                    "p-3 sm:p-4 rounded-lg border transition-all duration-300 hover:shadow-md cursor-pointer touch-manipulation",
                    priorityColors[recommendation.priority]
                  )}
                  onClick={() => onSkillSelect?.(recommendation.skill.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-2 sm:gap-3 flex-1">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center flex-shrink-0">
                        {renderIcon(recommendation.skill.icon, "w-4 h-4 sm:w-5 sm:h-5")}
                      </div>
                      <div className="flex-1 space-y-1 sm:space-y-2 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                          <h4 className="font-semibold text-sm sm:text-base truncate">{recommendation.skill.name}</h4>
                          <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                            <Badge
                              variant="outline"
                              className={cn(
                                "text-xs",
                                recommendation.priority === 'high' ? 'border-red-500/50 text-red-400' :
                                recommendation.priority === 'medium' ? 'border-yellow-500/50 text-yellow-400' :
                                'border-blue-500/50 text-blue-400'
                              )}
                            >
                              {recommendation.priority}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {recommendation.skill.difficulty}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                          {recommendation.reason}
                        </p>
                        <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {recommendation.estimatedTime}h
                          </span>
                          <span className="flex items-center gap-1">
                            <StatusIcon className={cn("w-3 h-3", statusColors[recommendation.skill.status])} />
                            <span className="hidden sm:inline">{recommendation.skill.status.replace('-', ' ')}</span>
                          </span>
                          {recommendation.prerequisites.length > 0 && (
                            <span className="flex items-center gap-1">
                              <AlertCircle className="w-3 h-3" />
                              <span className="hidden sm:inline">{recommendation.prerequisites.length} prerequisites met</span>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="ml-1 sm:ml-2 flex-shrink-0">
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )
            })}
          </CardContent>
        </Card>
      </motion.div>

      {/* Difficulty Progress */}
      <motion.div variants={itemVariants}>
        <Card className="bg-neutral-900/50 backdrop-blur-lg border-neutral-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Progress by Difficulty Level
            </CardTitle>
            <CardDescription>
              Your mastery across different skill complexity levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(progressStats.byDifficulty).map(([difficulty, progress]) => (
                <div key={difficulty} className="text-center space-y-2">
                  <div className="text-2xl font-bold text-primary">
                    {progress.completionPercentage}%
                  </div>
                  <div className="text-sm capitalize">{difficulty}</div>
                  <div className="text-xs text-muted-foreground">
                    {progress.completed}/{progress.total} skills
                  </div>
                  <Progress value={progress.completionPercentage} className="h-1" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
        </TabsContent>

        <TabsContent value="achievements" className="mt-8">
          <AchievementSystem onSkillSelect={onSkillSelect} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
