import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SkillsProgressTracker } from '../progressTracker'
import { skillsRoadmap } from '../roadmapData'
import SkillsRoadmapSection from '../SkillsRoadmapSection'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('Skills Roadmap System', () => {
  describe('SkillsProgressTracker', () => {
    it('should calculate progress correctly', () => {
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      const progress = tracker.calculateProgress()
      
      expect(progress.overall.totalSkills).toBeGreaterThan(0)
      expect(progress.overall.completionPercentage).toBeGreaterThanOrEqual(0)
      expect(progress.overall.completionPercentage).toBeLessThanOrEqual(100)
    })

    it('should generate learning recommendations', () => {
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      const recommendations = tracker.getLearningRecommendations()
      
      expect(Array.isArray(recommendations)).toBe(true)
      expect(recommendations.length).toBeGreaterThanOrEqual(0)
      
      if (recommendations.length > 0) {
        const recommendation = recommendations[0]
        expect(recommendation).toHaveProperty('skill')
        expect(recommendation).toHaveProperty('reason')
        expect(recommendation).toHaveProperty('priority')
        expect(recommendation).toHaveProperty('estimatedTime')
      }
    })

    it('should calculate skill impact correctly', () => {
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      const firstSkill = skillsRoadmap.nodes[0]
      const impact = tracker.calculateSkillImpact(firstSkill.id)
      
      expect(typeof impact).toBe('number')
      expect(impact).toBeGreaterThanOrEqual(0)
    })

    it('should get unlocked skills correctly', () => {
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      const firstSkill = skillsRoadmap.nodes[0]
      const unlockedSkills = tracker.getUnlockedSkills(firstSkill.id)
      
      expect(Array.isArray(unlockedSkills)).toBe(true)
    })
  })

  describe('Roadmap Data Structure', () => {
    it('should have valid roadmap structure', () => {
      expect(skillsRoadmap).toHaveProperty('nodes')
      expect(skillsRoadmap).toHaveProperty('connections')
      expect(skillsRoadmap).toHaveProperty('sections')
      expect(skillsRoadmap).toHaveProperty('metadata')
      
      expect(Array.isArray(skillsRoadmap.nodes)).toBe(true)
      expect(Array.isArray(skillsRoadmap.connections)).toBe(true)
      expect(Array.isArray(skillsRoadmap.sections)).toBe(true)
    })

    it('should have nodes with required properties', () => {
      const node = skillsRoadmap.nodes[0]
      
      expect(node).toHaveProperty('id')
      expect(node).toHaveProperty('name')
      expect(node).toHaveProperty('description')
      expect(node).toHaveProperty('status')
      expect(node).toHaveProperty('difficulty')
      expect(node).toHaveProperty('estimatedHours')
      expect(node).toHaveProperty('prerequisites')
      expect(node).toHaveProperty('unlocks')
      expect(node).toHaveProperty('path')
      expect(node).toHaveProperty('position')
    })

    it('should have valid connections', () => {
      skillsRoadmap.connections.forEach(connection => {
        expect(connection).toHaveProperty('from')
        expect(connection).toHaveProperty('to')
        expect(connection).toHaveProperty('type')
        expect(connection).toHaveProperty('strength')
        
        // Verify that connection references exist in nodes
        const fromNode = skillsRoadmap.nodes.find(n => n.id === connection.from)
        const toNode = skillsRoadmap.nodes.find(n => n.id === connection.to)
        
        expect(fromNode).toBeDefined()
        expect(toNode).toBeDefined()
      })
    })

    it('should have consistent prerequisite relationships', () => {
      skillsRoadmap.nodes.forEach(node => {
        node.prerequisites.forEach(prereqId => {
          const prereqNode = skillsRoadmap.nodes.find(n => n.id === prereqId)
          expect(prereqNode).toBeDefined()
          
          // Check if there's a corresponding connection
          const connection = skillsRoadmap.connections.find(c => 
            c.from === prereqId && c.to === node.id
          )
          expect(connection).toBeDefined()
        })
      })
    })
  })

  describe('SkillsRoadmapSection Component', () => {
    it('should render without crashing', () => {
      render(<SkillsRoadmapSection />)
      
      expect(screen.getByText('Skills')).toBeInTheDocument()
      expect(screen.getByText('Roadmap')).toBeInTheDocument()
    })

    it('should have interactive roadmap and overview tabs', () => {
      render(<SkillsRoadmapSection />)
      
      expect(screen.getByText('Interactive Roadmap')).toBeInTheDocument()
      expect(screen.getByText('Progress Overview')).toBeInTheDocument()
    })

    it('should switch between tabs correctly', () => {
      render(<SkillsRoadmapSection />)
      
      const overviewTab = screen.getByText('Progress Overview')
      fireEvent.click(overviewTab)
      
      // Should show progress dashboard content
      expect(screen.getByText('Progress')).toBeInTheDocument()
    })
  })

  describe('Performance Considerations', () => {
    it('should handle large skill trees efficiently', () => {
      const startTime = performance.now()
      
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      const progress = tracker.calculateProgress()
      const recommendations = tracker.getLearningRecommendations()
      
      const endTime = performance.now()
      const executionTime = endTime - startTime
      
      // Should complete calculations in reasonable time (< 100ms)
      expect(executionTime).toBeLessThan(100)
      expect(progress).toBeDefined()
      expect(recommendations).toBeDefined()
    })

    it('should memoize expensive calculations', () => {
      const tracker = new SkillsProgressTracker(skillsRoadmap)
      
      const startTime1 = performance.now()
      const progress1 = tracker.calculateProgress()
      const endTime1 = performance.now()
      
      const startTime2 = performance.now()
      const progress2 = tracker.calculateProgress()
      const endTime2 = performance.now()
      
      // Results should be consistent
      expect(progress1.overall.totalSkills).toBe(progress2.overall.totalSkills)
      expect(progress1.overall.completionPercentage).toBe(progress2.overall.completionPercentage)
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<SkillsRoadmapSection />)
      
      // Check for proper heading structure
      const mainHeading = screen.getByRole('heading', { level: 2 })
      expect(mainHeading).toBeInTheDocument()
      
      // Check for tab navigation
      const tabList = screen.getByRole('tablist')
      expect(tabList).toBeInTheDocument()
    })

    it('should support keyboard navigation', () => {
      render(<SkillsRoadmapSection />)
      
      const firstTab = screen.getByRole('tab', { name: /interactive roadmap/i })
      const secondTab = screen.getByRole('tab', { name: /progress overview/i })
      
      expect(firstTab).toBeInTheDocument()
      expect(secondTab).toBeInTheDocument()
      
      // Test keyboard navigation
      firstTab.focus()
      expect(document.activeElement).toBe(firstTab)
    })
  })
})
