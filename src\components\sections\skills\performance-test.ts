/**
 * Performance testing utilities for the Skills Roadmap system
 * Run this in the browser console to test performance
 */

import { skillsRoadmap } from './roadmapData'
import { SkillsProgressTracker } from './progressTracker'

export interface PerformanceTestResult {
  testName: string
  executionTime: number
  memoryUsage?: number
  iterations: number
  averageTime: number
  success: boolean
  error?: string
}

export class SkillsRoadmapPerformanceTester {
  private results: PerformanceTestResult[] = []

  /**
   * Test the performance of progress calculation
   */
  async testProgressCalculation(iterations: number = 100): Promise<PerformanceTestResult> {
    const testName = 'Progress Calculation'
    const tracker = new SkillsProgressTracker(skillsRoadmap)
    
    try {
      const startTime = performance.now()
      const startMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      for (let i = 0; i < iterations; i++) {
        tracker.calculateProgress()
      }
      
      const endTime = performance.now()
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0
      const executionTime = endTime - startTime
      const memoryUsage = endMemory - startMemory
      
      const result: PerformanceTestResult = {
        testName,
        executionTime,
        memoryUsage,
        iterations,
        averageTime: executionTime / iterations,
        success: true
      }
      
      this.results.push(result)
      return result
    } catch (error) {
      const result: PerformanceTestResult = {
        testName,
        executionTime: 0,
        iterations,
        averageTime: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      
      this.results.push(result)
      return result
    }
  }

  /**
   * Test the performance of learning recommendations
   */
  async testLearningRecommendations(iterations: number = 50): Promise<PerformanceTestResult> {
    const testName = 'Learning Recommendations'
    const tracker = new SkillsProgressTracker(skillsRoadmap)
    
    try {
      const startTime = performance.now()
      const startMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      for (let i = 0; i < iterations; i++) {
        tracker.getLearningRecommendations()
      }
      
      const endTime = performance.now()
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0
      const executionTime = endTime - startTime
      const memoryUsage = endMemory - startMemory
      
      const result: PerformanceTestResult = {
        testName,
        executionTime,
        memoryUsage,
        iterations,
        averageTime: executionTime / iterations,
        success: true
      }
      
      this.results.push(result)
      return result
    } catch (error) {
      const result: PerformanceTestResult = {
        testName,
        executionTime: 0,
        iterations,
        averageTime: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      
      this.results.push(result)
      return result
    }
  }

  /**
   * Test roadmap data structure validation
   */
  async testDataStructureValidation(): Promise<PerformanceTestResult> {
    const testName = 'Data Structure Validation'
    
    try {
      const startTime = performance.now()
      
      // Validate nodes
      const nodeIds = new Set(skillsRoadmap.nodes.map(n => n.id))
      if (nodeIds.size !== skillsRoadmap.nodes.length) {
        throw new Error('Duplicate node IDs found')
      }
      
      // Validate connections
      for (const connection of skillsRoadmap.connections) {
        if (!nodeIds.has(connection.from)) {
          throw new Error(`Connection references non-existent node: ${connection.from}`)
        }
        if (!nodeIds.has(connection.to)) {
          throw new Error(`Connection references non-existent node: ${connection.to}`)
        }
      }
      
      // Validate prerequisites
      for (const node of skillsRoadmap.nodes) {
        for (const prereqId of node.prerequisites) {
          if (!nodeIds.has(prereqId)) {
            throw new Error(`Node ${node.id} references non-existent prerequisite: ${prereqId}`)
          }
        }
      }
      
      const endTime = performance.now()
      const executionTime = endTime - startTime
      
      const result: PerformanceTestResult = {
        testName,
        executionTime,
        iterations: 1,
        averageTime: executionTime,
        success: true
      }
      
      this.results.push(result)
      return result
    } catch (error) {
      const result: PerformanceTestResult = {
        testName,
        executionTime: 0,
        iterations: 1,
        averageTime: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      
      this.results.push(result)
      return result
    }
  }

  /**
   * Test memory usage with large datasets
   */
  async testMemoryUsage(): Promise<PerformanceTestResult> {
    const testName = 'Memory Usage Test'
    
    try {
      const startTime = performance.now()
      const startMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Create multiple tracker instances to test memory usage
      const trackers = []
      for (let i = 0; i < 10; i++) {
        const tracker = new SkillsProgressTracker(skillsRoadmap)
        tracker.calculateProgress()
        tracker.getLearningRecommendations()
        trackers.push(tracker)
      }
      
      const endTime = performance.now()
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0
      const executionTime = endTime - startTime
      const memoryUsage = endMemory - startMemory
      
      // Clean up
      trackers.length = 0
      
      const result: PerformanceTestResult = {
        testName,
        executionTime,
        memoryUsage,
        iterations: 10,
        averageTime: executionTime / 10,
        success: memoryUsage < 10 * 1024 * 1024 // Should use less than 10MB
      }
      
      if (!result.success) {
        result.error = `Memory usage too high: ${Math.round(memoryUsage / 1024 / 1024)}MB`
      }
      
      this.results.push(result)
      return result
    } catch (error) {
      const result: PerformanceTestResult = {
        testName,
        executionTime: 0,
        iterations: 10,
        averageTime: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      
      this.results.push(result)
      return result
    }
  }

  /**
   * Run all performance tests
   */
  async runAllTests(): Promise<PerformanceTestResult[]> {
    console.log('🚀 Starting Skills Roadmap Performance Tests...')
    
    const tests = [
      () => this.testDataStructureValidation(),
      () => this.testProgressCalculation(100),
      () => this.testLearningRecommendations(50),
      () => this.testMemoryUsage()
    ]
    
    for (const test of tests) {
      const result = await test()
      console.log(`${result.success ? '✅' : '❌'} ${result.testName}:`, {
        executionTime: `${result.executionTime.toFixed(2)}ms`,
        averageTime: `${result.averageTime.toFixed(2)}ms`,
        memoryUsage: result.memoryUsage ? `${Math.round(result.memoryUsage / 1024)}KB` : 'N/A',
        error: result.error
      })
    }
    
    const successCount = this.results.filter(r => r.success).length
    const totalCount = this.results.length
    
    console.log(`\n📊 Performance Test Summary: ${successCount}/${totalCount} tests passed`)
    
    if (successCount === totalCount) {
      console.log('🎉 All performance tests passed! The roadmap system is optimized.')
    } else {
      console.warn('⚠️ Some performance tests failed. Consider optimization.')
    }
    
    return this.results
  }

  /**
   * Get test results
   */
  getResults(): PerformanceTestResult[] {
    return [...this.results]
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.results = []
  }
}

// Export a singleton instance for easy testing
export const performanceTester = new SkillsRoadmapPerformanceTester()

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).skillsRoadmapPerformanceTester = performanceTester
}
