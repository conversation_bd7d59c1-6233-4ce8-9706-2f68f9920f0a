import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import SkillsRoadmapVisualization from './SkillsRoadmapVisualization'

export default function SkillsRoadmapSection({ className }: BaseSectionProps) {

  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            INTERACTIVE LEARNING JOURNEY
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          Skills{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Roadmap
          </span>
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-lg text-muted-foreground mt-4 max-w-3xl mx-auto"
        >
          Explore my comprehensive full-stack development journey from foundational web technologies 
          to advanced frameworks and tools. Click on any skill to see detailed information, prerequisites, 
          and learning resources.
        </motion.p>
      </div>

      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
        className="max-w-7xl mx-auto"
      >
        <motion.div variants={itemVariants}>
          <SkillsRoadmapVisualization />
        </motion.div>
      </motion.div>
    </section>
  )
}
