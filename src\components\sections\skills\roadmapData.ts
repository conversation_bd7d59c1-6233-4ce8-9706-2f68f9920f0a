import {
  SiHtml5,
  SiCss3,
  SiJavascript,
  SiReact,
  SiTypescript,
  SiNextdotjs,
  SiTailwindcss,
  SiSass,
  SiNodedotjs,
  SiExpress,
  SiPostgresql,
  SiMongodb,
  SiPrisma,
  SiGit,
  SiGithub,
  SiDocker,
  SiAmazonaws,
  SiVercel,
  SiNetlify,
  SiVitest,
  SiJest,
  SiCypress,
  SiWebpack,
  SiVite,
  SiFigma,
  SiGraphql,
  SiRedis,
  SiSocketdotio
} from "react-icons/si"
import {
  Database,
  Server,
  Globe,
  Shield,
  Zap,
  Settings,
  Code,
  Palette,
  Monitor,
  Smartphone,
  Cloud,
  Lock,
  Search,
  BarChart3,
  Layers,
  Package,
  Terminal,
  GitBranch,
  TestTube,
  Rocket,
  Eye,
  Users,
  MessageSquare,
  Timer,
  Target,
  BookOpen,
  Award,
  Lightbulb,
  Brain,
  Wrench,
  CheckCircle,
  Star,
  TrendingUp,
  Activity
} from "lucide-react"
import { RoadmapNode, RoadmapConnection, RoadmapSection, SkillsRoadmap } from './types'

// Foundation Skills (Starting Point)
const foundationNodes: RoadmapNode[] = [
  {
    id: 'html5',
    name: 'HTML5',
    description: 'Semantic markup, accessibility, web standards, and modern HTML features',
    icon: SiHtml5,
    status: 'completed',
    difficulty: 'beginner',
    estimatedHours: 40,
    category: 'Foundation',
    path: ['frontend', 'fullstack'],
    prerequisites: [],
    unlocks: ['css3', 'javascript-basics'],
    position: { x: 400, y: 100 },
    resources: [
      { name: 'MDN HTML Guide', type: 'documentation', url: 'https://developer.mozilla.org/en-US/docs/Web/HTML', free: true },
      { name: 'HTML5 Semantic Elements', type: 'tutorial', free: true },
      { name: 'Web Accessibility Basics', type: 'course', free: true }
    ],
    projects: ['Personal Portfolio', 'Landing Page', 'Blog Template'],
    color: '#E34F26',
    level: 98
  },
  {
    id: 'css3',
    name: 'CSS3',
    description: 'Flexbox, Grid, animations, responsive design, and modern CSS features',
    icon: SiCss3,
    status: 'completed',
    difficulty: 'beginner',
    estimatedHours: 60,
    category: 'Foundation',
    path: ['frontend', 'fullstack', 'design'],
    prerequisites: ['html5'],
    unlocks: ['responsive-design', 'css-preprocessors', 'css-frameworks'],
    position: { x: 250, y: 250 },
    resources: [
      { name: 'CSS Grid Complete Guide', type: 'tutorial', free: true },
      { name: 'Flexbox Froggy', type: 'practice', free: true },
      { name: 'CSS Animation Masterclass', type: 'course', free: false }
    ],
    projects: ['Responsive Website', 'CSS Art', 'Animation Showcase'],
    color: '#1572B6',
    level: 95
  },
  {
    id: 'javascript-basics',
    name: 'JavaScript Fundamentals',
    description: 'Variables, functions, objects, arrays, and basic programming concepts',
    icon: SiJavascript,
    status: 'completed',
    difficulty: 'beginner',
    estimatedHours: 80,
    category: 'Foundation',
    path: ['frontend', 'backend', 'fullstack'],
    prerequisites: ['html5'],
    unlocks: ['javascript-advanced', 'dom-manipulation', 'es6-features'],
    position: { x: 550, y: 250 },
    resources: [
      { name: 'JavaScript.info', type: 'tutorial', free: true },
      { name: 'Eloquent JavaScript', type: 'book', free: true },
      { name: 'JavaScript30', type: 'practice', free: true }
    ],
    projects: ['Calculator', 'Todo List', 'Simple Games'],
    color: '#F7DF1E',
    level: 95
  }
]

// Frontend Development Path
const frontendNodes: RoadmapNode[] = [
  {
    id: 'responsive-design',
    name: 'Responsive Design',
    description: 'Mobile-first design, media queries, and cross-device compatibility',
    icon: Smartphone,
    status: 'completed',
    difficulty: 'intermediate',
    estimatedHours: 30,
    category: 'Frontend',
    path: ['frontend', 'fullstack', 'design'],
    prerequisites: ['css3'],
    unlocks: ['css-frameworks'],
    position: { x: 200, y: 400 },
    resources: [
      { name: 'Responsive Web Design Principles', type: 'course', free: true },
      { name: 'Mobile-First Design Guide', type: 'tutorial', free: true }
    ],
    projects: ['Responsive Portfolio', 'Mobile App Landing'],
    color: '#FF6B6B',
    level: 92
  },
  {
    id: 'javascript-advanced',
    name: 'Advanced JavaScript',
    description: 'Closures, prototypes, async/await, promises, and advanced patterns',
    icon: SiJavascript,
    status: 'completed',
    difficulty: 'intermediate',
    estimatedHours: 100,
    category: 'Frontend',
    path: ['frontend', 'backend', 'fullstack'],
    prerequisites: ['javascript-basics'],
    unlocks: ['typescript', 'react', 'nodejs'],
    position: { x: 600, y: 400 },
    resources: [
      { name: 'You Don\'t Know JS', type: 'book', free: true },
      { name: 'Advanced JavaScript Concepts', type: 'course', free: false }
    ],
    projects: ['Async Data Fetcher', 'Module System', 'Design Patterns'],
    color: '#F7DF1E',
    level: 90
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    description: 'Static typing, interfaces, generics, and type-safe development',
    icon: SiTypescript,
    status: 'completed',
    difficulty: 'intermediate',
    estimatedHours: 60,
    category: 'Frontend',
    path: ['frontend', 'backend', 'fullstack'],
    prerequisites: ['javascript-advanced'],
    unlocks: ['react-typescript', 'nodejs-typescript'],
    position: { x: 400, y: 550 },
    resources: [
      { name: 'TypeScript Handbook', type: 'documentation', free: true },
      { name: 'TypeScript Deep Dive', type: 'book', free: true }
    ],
    projects: ['Type-Safe API Client', 'Generic Utilities', 'Advanced Types'],
    color: '#3178C6',
    level: 90
  },
  {
    id: 'react',
    name: 'React',
    description: 'Components, hooks, state management, and modern React patterns',
    icon: SiReact,
    status: 'completed',
    difficulty: 'intermediate',
    estimatedHours: 120,
    category: 'Frontend',
    path: ['frontend', 'fullstack'],
    prerequisites: ['javascript-advanced'],
    unlocks: ['react-typescript', 'nextjs', 'state-management'],
    position: { x: 300, y: 700 },
    resources: [
      { name: 'React Official Docs', type: 'documentation', free: true },
      { name: 'Epic React', type: 'course', free: false },
      { name: 'React Patterns', type: 'tutorial', free: true }
    ],
    projects: ['Todo App', 'Weather Dashboard', 'E-commerce Frontend'],
    color: '#61DAFB',
    level: 95
  }
]

// Advanced Frontend & Frameworks
const advancedFrontendNodes: RoadmapNode[] = [
  {
    id: 'react-typescript',
    name: 'React + TypeScript',
    description: 'Type-safe React development with advanced patterns',
    icon: SiReact,
    status: 'completed',
    difficulty: 'advanced',
    estimatedHours: 80,
    category: 'Frontend',
    path: ['frontend', 'fullstack'],
    prerequisites: ['react', 'typescript'],
    unlocks: ['nextjs', 'state-management'],
    position: { x: 500, y: 700 },
    resources: [
      { name: 'React TypeScript Cheatsheet', type: 'documentation', free: true },
      { name: 'Advanced React with TypeScript', type: 'course', free: false }
    ],
    projects: ['Type-Safe Component Library', 'Advanced React App'],
    color: '#61DAFB',
    level: 88
  },
  {
    id: 'nextjs',
    name: 'Next.js',
    description: 'Full-stack React framework with SSR, SSG, and API routes',
    icon: SiNextdotjs,
    status: 'completed',
    difficulty: 'advanced',
    estimatedHours: 100,
    category: 'Frontend',
    path: ['frontend', 'fullstack'],
    prerequisites: ['react'],
    unlocks: ['fullstack-development'],
    position: { x: 400, y: 850 },
    resources: [
      { name: 'Next.js Documentation', type: 'documentation', free: true },
      { name: 'Next.js Masterclass', type: 'course', free: false }
    ],
    projects: ['Blog Platform', 'E-commerce Site', 'Portfolio Website'],
    color: '#000000',
    level: 85
  },
  {
    id: 'tailwind-css',
    name: 'Tailwind CSS',
    description: 'Utility-first CSS framework for rapid UI development',
    icon: SiTailwindcss,
    status: 'completed',
    difficulty: 'intermediate',
    estimatedHours: 40,
    category: 'Frontend',
    path: ['frontend', 'fullstack', 'design'],
    prerequisites: ['css3', 'responsive-design'],
    unlocks: ['component-libraries'],
    position: { x: 150, y: 700 },
    resources: [
      { name: 'Tailwind CSS Docs', type: 'documentation', free: true },
      { name: 'Tailwind UI Components', type: 'tutorial', free: false }
    ],
    projects: ['Design System', 'Component Library', 'Landing Pages'],
    color: '#06B6D4',
    level: 92
  },
  {
    id: 'state-management',
    name: 'State Management',
    description: 'Redux, Zustand, Context API, and global state patterns',
    icon: Package,
    status: 'in-progress',
    difficulty: 'advanced',
    estimatedHours: 60,
    category: 'Frontend',
    path: ['frontend', 'fullstack'],
    prerequisites: ['react'],
    unlocks: ['advanced-react-patterns'],
    position: { x: 200, y: 850 },
    resources: [
      { name: 'Redux Toolkit Guide', type: 'documentation', free: true },
      { name: 'State Management Patterns', type: 'course', free: false }
    ],
    projects: ['Complex Dashboard', 'Multi-User App', 'Real-time Chat'],
    color: '#764ABC',
    level: 75
  }
]

// Backend Development Path
const backendNodes: RoadmapNode[] = [
  {
    id: 'nodejs',
    name: 'Node.js',
    description: 'Server-side JavaScript runtime and ecosystem',
    icon: SiNodedotjs,
    status: 'in-progress',
    difficulty: 'intermediate',
    estimatedHours: 80,
    category: 'Backend',
    path: ['backend', 'fullstack'],
    prerequisites: ['javascript-advanced'],
    unlocks: ['express', 'nodejs-typescript'],
    position: { x: 650, y: 550 },
    resources: [
      { name: 'Node.js Documentation', type: 'documentation', free: true },
      { name: 'Node.js Complete Guide', type: 'course', free: false }
    ],
    projects: ['CLI Tool', 'File System App', 'Basic Server'],
    color: '#339933',
    level: 75
  },
  {
    id: 'express',
    name: 'Express.js',
    description: 'Web framework for Node.js with middleware and routing',
    icon: SiExpress,
    status: 'in-progress',
    difficulty: 'intermediate',
    estimatedHours: 60,
    category: 'Backend',
    path: ['backend', 'fullstack'],
    prerequisites: ['nodejs'],
    unlocks: ['rest-apis', 'authentication'],
    position: { x: 600, y: 850 },
    resources: [
      { name: 'Express.js Guide', type: 'documentation', free: true },
      { name: 'Building APIs with Express', type: 'course', free: false }
    ],
    projects: ['REST API', 'Web Server', 'Middleware System'],
    color: '#000000',
    level: 78
  },
  {
    id: 'databases',
    name: 'Databases',
    description: 'SQL and NoSQL databases, design patterns, and optimization',
    icon: Database,
    status: 'in-progress',
    difficulty: 'intermediate',
    estimatedHours: 100,
    category: 'Backend',
    path: ['backend', 'fullstack'],
    prerequisites: [],
    unlocks: ['postgresql', 'mongodb', 'prisma'],
    position: { x: 750, y: 700 },
    resources: [
      { name: 'Database Design Fundamentals', type: 'course', free: true },
      { name: 'SQL vs NoSQL Guide', type: 'tutorial', free: true }
    ],
    projects: ['Database Schema', 'Data Migration', 'Query Optimization'],
    color: '#336791',
    level: 70
  }
]

export const roadmapNodes: RoadmapNode[] = [
  ...foundationNodes,
  ...frontendNodes,
  ...advancedFrontendNodes,
  ...backendNodes
]

// Connections between skills
export const roadmapConnections: RoadmapConnection[] = [
  // Foundation connections
  { from: 'html5', to: 'css3', type: 'prerequisite', strength: 3 },
  { from: 'html5', to: 'javascript-basics', type: 'prerequisite', strength: 3 },
  { from: 'css3', to: 'responsive-design', type: 'prerequisite', strength: 2 },
  { from: 'css3', to: 'tailwind-css', type: 'recommended', strength: 2 },
  { from: 'javascript-basics', to: 'javascript-advanced', type: 'prerequisite', strength: 3 },

  // Frontend progression
  { from: 'javascript-advanced', to: 'typescript', type: 'recommended', strength: 2 },
  { from: 'javascript-advanced', to: 'react', type: 'prerequisite', strength: 3 },
  { from: 'javascript-advanced', to: 'nodejs', type: 'prerequisite', strength: 2 },
  { from: 'react', to: 'react-typescript', type: 'recommended', strength: 2 },
  { from: 'react', to: 'nextjs', type: 'recommended', strength: 2 },
  { from: 'react', to: 'state-management', type: 'recommended', strength: 2 },
  { from: 'typescript', to: 'react-typescript', type: 'prerequisite', strength: 3 },
  { from: 'responsive-design', to: 'tailwind-css', type: 'recommended', strength: 1 },

  // Backend progression
  { from: 'nodejs', to: 'express', type: 'recommended', strength: 2 },
  { from: 'databases', to: 'postgresql', type: 'optional', strength: 1 },
  { from: 'databases', to: 'mongodb', type: 'optional', strength: 1 },

  // Cross-path connections
  { from: 'react-typescript', to: 'nextjs', type: 'recommended', strength: 2 },
  { from: 'nextjs', to: 'express', type: 'optional', strength: 1 },
  { from: 'express', to: 'databases', type: 'recommended', strength: 2 }
]

// Roadmap sections for organization
export const roadmapSections: RoadmapSection[] = [
  {
    id: 'foundation',
    name: 'Foundation',
    description: 'Essential web development fundamentals',
    color: '#4F46E5',
    nodes: ['html5', 'css3', 'javascript-basics'],
    position: { x: 50, y: 50, width: 600, height: 200 }
  },
  {
    id: 'frontend',
    name: 'Frontend Development',
    description: 'Modern frontend technologies and frameworks',
    color: '#06B6D4',
    nodes: ['responsive-design', 'javascript-advanced', 'typescript', 'react', 'react-typescript', 'nextjs', 'tailwind-css', 'state-management'],
    position: { x: 50, y: 300, width: 800, height: 400 }
  },
  {
    id: 'backend',
    name: 'Backend Development',
    description: 'Server-side development and databases',
    color: '#10B981',
    nodes: ['nodejs', 'express', 'databases'],
    position: { x: 850, y: 200, width: 400, height: 300 }
  }
]

// Complete roadmap structure
export const skillsRoadmap: SkillsRoadmap = {
  nodes: roadmapNodes,
  connections: roadmapConnections,
  sections: roadmapSections,
  metadata: {
    totalSkills: roadmapNodes.length,
    completedSkills: roadmapNodes.filter(node => node.status === 'completed').length,
    inProgressSkills: roadmapNodes.filter(node => node.status === 'in-progress').length,
    estimatedTotalHours: roadmapNodes.reduce((total, node) => total + node.estimatedHours, 0),
    lastUpdated: new Date().toISOString()
  }
}
