import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Clock, Award, Target, BookOpen, ExternalLink, CheckCircle, Circle, AlertCircle, Lock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { RoadmapNode, SkillStatus } from './types'
import { renderIcon } from './utils'
import { skillsRoadmap } from './roadmapData'

interface SkillNodeDetailProps {
  node: RoadmapNode | null
  isOpen: boolean
  onClose: () => void
  className?: string
}

const statusConfig = {
  completed: {
    icon: CheckCircle,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20',
    label: 'Completed'
  },
  'in-progress': {
    icon: Circle,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/20',
    label: 'In Progress'
  },
  available: {
    icon: Target,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    label: 'Available'
  },
  locked: {
    icon: Lock,
    color: 'text-gray-500',
    bgColor: 'bg-gray-500/10',
    borderColor: 'border-gray-500/20',
    label: 'Locked'
  }
}

const difficultyConfig = {
  beginner: { color: 'text-green-500', bgColor: 'bg-green-500/10', label: 'Beginner' },
  intermediate: { color: 'text-yellow-500', bgColor: 'bg-yellow-500/10', label: 'Intermediate' },
  advanced: { color: 'text-red-500', bgColor: 'bg-red-500/10', label: 'Advanced' },
  expert: { color: 'text-purple-500', bgColor: 'bg-purple-500/10', label: 'Expert' }
}

export default function SkillNodeDetail({ node, isOpen, onClose, className }: SkillNodeDetailProps) {
  if (!node) return null

  const statusInfo = statusConfig[node.status]
  const difficultyInfo = difficultyConfig[node.difficulty]
  const StatusIcon = statusInfo.icon
  
  // Get prerequisite and unlocked skills
  const prerequisites = node.prerequisites.map(id => 
    skillsRoadmap.nodes.find(n => n.id === id)
  ).filter(Boolean)
  
  const unlockedSkills = node.unlocks.map(id => 
    skillsRoadmap.nodes.find(n => n.id === id)
  ).filter(Boolean)

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",
              "w-full max-w-2xl max-h-[90vh] overflow-y-auto",
              "m-4",
              className
            )}
          >
            <Card className="bg-neutral-900/95 backdrop-blur-lg border-neutral-800">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div 
                      className="w-12 h-12 rounded-xl flex items-center justify-center"
                      style={{ backgroundColor: node.color + '20' }}
                    >
                      {renderIcon(node.icon, "w-6 h-6")}
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold">{node.name}</CardTitle>
                      <CardDescription className="text-sm mt-1">
                        {node.category} • {node.path.join(', ')}
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Status and Difficulty Badges */}
                <div className="flex items-center gap-3 mt-4">
                  <Badge 
                    variant="secondary" 
                    className={cn(statusInfo.bgColor, statusInfo.borderColor, statusInfo.color, "border")}
                  >
                    <StatusIcon className="w-3 h-3 mr-1" />
                    {statusInfo.label}
                  </Badge>
                  <Badge 
                    variant="secondary" 
                    className={cn(difficultyInfo.bgColor, difficultyInfo.color)}
                  >
                    {difficultyInfo.label}
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {node.estimatedHours}h
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Description */}
                <div>
                  <h4 className="font-semibold mb-2">Description</h4>
                  <p className="text-muted-foreground leading-relaxed">{node.description}</p>
                </div>
                
                {/* Progress (if applicable) */}
                {node.level && (node.status === 'completed' || node.status === 'in-progress') && (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Progress</h4>
                      <span className="text-sm text-muted-foreground">{node.level}%</span>
                    </div>
                    <Progress value={node.level} className="h-2" />
                  </div>
                )}
                
                {/* Prerequisites */}
                {prerequisites.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      Prerequisites
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {prerequisites.map((prereq) => (
                        <div
                          key={prereq.id}
                          className="flex items-center gap-2 p-2 rounded-lg bg-neutral-800/50 border border-neutral-700"
                        >
                          <div className="w-6 h-6 flex items-center justify-center">
                            {renderIcon(prereq.icon, "w-4 h-4")}
                          </div>
                          <span className="text-sm">{prereq.name}</span>
                          <CheckCircle className="w-3 h-3 text-green-500 ml-auto" />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Unlocked Skills */}
                {unlockedSkills.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      Unlocks
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {unlockedSkills.map((skill) => (
                        <div
                          key={skill.id}
                          className="flex items-center gap-2 p-2 rounded-lg bg-neutral-800/50 border border-neutral-700"
                        >
                          <div className="w-6 h-6 flex items-center justify-center">
                            {renderIcon(skill.icon, "w-4 h-4")}
                          </div>
                          <span className="text-sm">{skill.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Learning Resources */}
                {node.resources.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      Learning Resources
                    </h4>
                    <div className="space-y-2">
                      {node.resources.map((resource, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg bg-neutral-800/50 border border-neutral-700"
                        >
                          <div className="flex items-center gap-3">
                            <Badge variant="outline" className="text-xs">
                              {resource.type}
                            </Badge>
                            <span className="text-sm">{resource.name}</span>
                            {resource.free && (
                              <Badge variant="secondary" className="text-xs bg-green-500/10 text-green-500">
                                Free
                              </Badge>
                            )}
                          </div>
                          {resource.url && (
                            <Button variant="ghost" size="sm" asChild>
                              <a href={resource.url} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Projects */}
                {node.projects && node.projects.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Award className="w-4 h-4" />
                      Related Projects
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {node.projects.map((project, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {project}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
